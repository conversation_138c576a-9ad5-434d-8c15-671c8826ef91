import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import '../providers/app_state.dart';
import '../theme/app_theme.dart';
import '../screens/profile_screen.dart';

class TitleAchievementDialog extends StatelessWidget {
  final String newTitle;
  final List<BreathingTechnique> unlockedTechniques;
  final AppState appState;

  const TitleAchievementDialog({
    super.key,
    required this.newTitle,
    required this.unlockedTechniques,
    required this.appState,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Responsive calculations
    final dialogWidth = (screenWidth * 0.9).clamp(300.0, 500.0);
    final dialogPadding = (screenWidth * 0.05).clamp(16.0, 24.0);
    final borderRadius = (screenWidth * 0.05).clamp(16.0, 24.0);

    // Typography scaling
    final headerFontSize = (screenWidth * 0.055).clamp(18.0, 24.0);
    final titleFontSize = (screenWidth * 0.07).clamp(22.0, 32.0);
    final bodyFontSize = (screenWidth * 0.04).clamp(14.0, 18.0);
    final buttonFontSize = (screenWidth * 0.038).clamp(13.0, 16.0);

    // Spacing calculations
    final verticalSpacing = (screenHeight * 0.02).clamp(12.0, 20.0);
    final smallVerticalSpacing = (screenHeight * 0.015).clamp(8.0, 16.0);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: dialogWidth,
          maxHeight: screenHeight * 0.85, // Prevent dialog from being too tall
        ),
        child: Container(
          width: dialogWidth,
          padding: EdgeInsets.all(dialogPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: const [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10.0,
                offset: Offset(0.0, 10.0),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Text(
                  'تبریک! شما به سطح جدیدی رسیدید!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: headerFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: verticalSpacing),

                // Profile Image
                Container(
                  width: (screenWidth * 0.25).clamp(80.0, 120.0),
                  height: (screenWidth * 0.25).clamp(80.0, 120.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppTheme.primaryColor,
                      width: (screenWidth * 0.008).clamp(2.0, 4.0),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor
                            .withAlpha((0.3 * 255).toInt()),
                        blurRadius: (screenWidth * 0.025).clamp(8.0, 12.0),
                        offset:
                            Offset(0, (screenWidth * 0.012).clamp(3.0, 6.0)),
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      appState.getProfileImagePath(),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppTheme.primaryColor
                              .withAlpha((0.1 * 255).toInt()),
                          child: Icon(
                            Icons.person,
                            size: (screenWidth * 0.12).clamp(30.0, 60.0),
                            color: AppTheme.primaryColor,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                SizedBox(height: smallVerticalSpacing),

                // New Title Display
                Text(
                  newTitle,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: smallVerticalSpacing),

                // Encouragement Message
                Text(
                  'شما با تلاش و پشتکار خود به این مقام دست یافتید!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: bodyFontSize,
                    color: Colors.grey[700],
                    fontStyle: FontStyle.italic,
                  ),
                ),
                SizedBox(height: verticalSpacing),

                // Unlocked Content Section
                if (unlockedTechniques.isNotEmpty) ...[
                  Container(
                    padding: EdgeInsets.all(dialogPadding * 0.75),
                    decoration: BoxDecoration(
                      color:
                          AppTheme.primaryColor.withAlpha((0.05 * 255).toInt()),
                      borderRadius: BorderRadius.circular(borderRadius * 0.6),
                      border: Border.all(
                        color: AppTheme.primaryColor
                            .withAlpha((0.2 * 255).toInt()),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              FeatherIcons.unlock,
                              color: AppTheme.primaryColor,
                              size: (screenWidth * 0.05).clamp(16.0, 24.0),
                            ),
                            SizedBox(
                                width: (screenWidth * 0.02).clamp(6.0, 10.0)),
                            Expanded(
                              child: Text(
                                'با این عنوان، تمرینات زیر برای شما باز شد:',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Samim',
                                  fontSize: bodyFontSize,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: smallVerticalSpacing),
                        ...unlockedTechniques.map((technique) =>
                            _buildUnlockedTechniqueItem(
                                technique, screenWidth, bodyFontSize)),
                      ],
                    ),
                  ),
                  SizedBox(height: verticalSpacing),
                ],

                // Action Buttons - Adaptive Layout
                _buildActionButtons(context, screenWidth, screenHeight,
                    buttonFontSize, borderRadius),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Responsive Action Buttons
  Widget _buildActionButtons(BuildContext context, double screenWidth,
      double screenHeight, double buttonFontSize, double borderRadius) {
    final buttonHeight = (screenHeight * 0.06).clamp(44.0, 56.0);
    final buttonSpacing = (screenWidth * 0.03).clamp(8.0, 16.0);
    final isNarrowScreen = screenWidth < 600;

    if (isNarrowScreen) {
      // Vertical layout for narrow screens
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: buttonHeight,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(borderRadius * 0.4),
                ),
              ),
              child: Text(
                'فوق‌العاده است!',
                style: TextStyle(
                  fontFamily: 'Samim',
                  fontSize: buttonFontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(height: buttonSpacing),
          SizedBox(
            width: double.infinity,
            height: buttonHeight,
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
              style: TextButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(borderRadius * 0.4),
                  side: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
              child: Text(
                'مشاهده پروفایل',
                style: TextStyle(
                  fontFamily: 'Samim',
                  fontSize: buttonFontSize,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      // Horizontal layout for wider screens
      return Row(
        children: [
          Expanded(
            child: SizedBox(
              height: buttonHeight,
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ProfileScreen(),
                    ),
                  );
                },
                style: TextButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(borderRadius * 0.4),
                    side: BorderSide(color: AppTheme.primaryColor),
                  ),
                ),
                child: Text(
                  'مشاهده پروفایل',
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: buttonFontSize,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: buttonSpacing),
          Expanded(
            flex: 2,
            child: SizedBox(
              height: buttonHeight,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(borderRadius * 0.4),
                  ),
                ),
                child: Text(
                  'فوق‌العاده است!',
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: buttonFontSize,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildUnlockedTechniqueItem(
      BreathingTechnique technique, double screenWidth, double bodyFontSize) {
    IconData getIconData(String name) {
      switch (name) {
        case 'wind':
          return FeatherIcons.wind;
        case 'moon':
          return FeatherIcons.moon;
        case 'square':
          return FeatherIcons.square;
        case 'git-branch':
          return FeatherIcons.gitBranch;
        case 'sunrise':
          return FeatherIcons.sunrise;
        case 'headphones':
          return FeatherIcons.headphones;
        case 'zap':
          return FeatherIcons.zap;
        default:
          return FeatherIcons.activity;
      }
    }

    // Responsive sizing
    final iconSize = (screenWidth * 0.06).clamp(18.0, 28.0);
    final iconPadding = (screenWidth * 0.025).clamp(8.0, 12.0);
    final itemSpacing = (screenWidth * 0.03).clamp(8.0, 16.0);
    final verticalPadding = (screenWidth * 0.015).clamp(4.0, 8.0);
    final titleFontSize = (bodyFontSize * 1.1).clamp(12.0, 16.0);
    final descriptionFontSize = (bodyFontSize * 0.9).clamp(10.0, 14.0);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(iconPadding),
            decoration: BoxDecoration(
              color: technique.color.withAlpha((0.15 * 255).toInt()),
              borderRadius: BorderRadius.circular(iconPadding),
            ),
            child: Icon(
              getIconData(technique.iconName),
              color: technique.color,
              size: iconSize,
            ),
          ),
          SizedBox(width: itemSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  technique.name,
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: verticalPadding * 0.5),
                Text(
                  technique.description,
                  style: TextStyle(
                    fontFamily: 'Samim',
                    fontSize: descriptionFontSize,
                    color: Colors.grey[600],
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
